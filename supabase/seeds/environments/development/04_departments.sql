-- Start transaction
BEGIN;

-- Create departments for ALL facilities
DO $$
DECLARE
  facility_record RECORD;
  dept_count INTEGER;
  i INTEGER;
  dept_name TEXT;
  dept_type department_type;
  dept_description TEXT;

  -- Department options based on facility type
  hospital_departments TEXT[] := ARRAY['Emergency Medicine', 'Internal Medicine', 'Surgery', 'Cardiology', 'Neurology', 'Orthopedics', 'Pediatrics', 'Obstetrics', 'Radiology', 'Laboratory'];
  clinic_departments TEXT[] := ARRAY['Primary Care', 'Family Medicine', 'Urgent Care', 'Preventive Care', 'Wellness'];
  specialty_departments TEXT[] := ARRAY['Specialty Care', 'Consultation', 'Treatment', 'Follow-up Care'];
BEGIN
  -- Loop through each facility and create appropriate departments
  FOR facility_record IN
    SELECT f.id, f.name, f.type, o.name as org_name, o.type as org_type
    FROM facilities f
    JOIN organizations o ON f.organization_id = o.id
  LOOP
    -- Determine number of departments based on facility type
    CASE facility_record.type
      WHEN 'hospital' THEN dept_count := 3 + floor(random() * 4)::int; -- 3-6 departments
      WHEN 'clinic' THEN dept_count := 1 + floor(random() * 3)::int;   -- 1-3 departments
      WHEN 'specialty' THEN dept_count := 1 + floor(random() * 2)::int; -- 1-2 departments
      ELSE dept_count := 2;
    END CASE;

    -- Create departments for this facility
    FOR i IN 1..dept_count LOOP
      -- Select appropriate department name and type
      CASE facility_record.type
        WHEN 'hospital' THEN
          dept_name := hospital_departments[1 + (random() * (array_length(hospital_departments, 1) - 1))::int];
          CASE dept_name
            WHEN 'Emergency Medicine' THEN
              dept_type := 'emergency'::department_type;
              dept_description := 'Emergency and urgent care services';
            WHEN 'Internal Medicine' THEN
              dept_type := 'internal_medicine'::department_type;
              dept_description := 'Internal medicine and general care';
            WHEN 'Surgery' THEN
              dept_type := 'surgery'::department_type;
              dept_description := 'Surgical services and procedures';
            WHEN 'Cardiology' THEN
              dept_type := 'cardiology'::department_type;
              dept_description := 'Heart and cardiovascular care';
            WHEN 'Neurology' THEN
              dept_type := 'neurology'::department_type;
              dept_description := 'Neurological care and treatment';
            WHEN 'Orthopedics' THEN
              dept_type := 'orthopedics'::department_type;
              dept_description := 'Bone and joint care';
            WHEN 'Pediatrics' THEN
              dept_type := 'pediatrics'::department_type;
              dept_description := 'Child healthcare services';
            WHEN 'Obstetrics' THEN
              dept_type := 'obstetrics'::department_type;
              dept_description := 'Maternity and women\'s health';
            WHEN 'Radiology' THEN
              dept_type := 'radiology'::department_type;
              dept_description := 'Medical imaging services';
            ELSE
              dept_type := 'other'::department_type;
              dept_description := 'Laboratory and diagnostic services';
          END CASE;

        WHEN 'clinic' THEN
          dept_name := clinic_departments[1 + (random() * (array_length(clinic_departments, 1) - 1))::int];
          CASE dept_name
            WHEN 'Primary Care' THEN
              dept_type := 'primary_care'::department_type;
              dept_description := 'Primary healthcare services';
            WHEN 'Family Medicine' THEN
              dept_type := 'primary_care'::department_type;
              dept_description := 'Family medicine and general care';
            WHEN 'Urgent Care' THEN
              dept_type := 'emergency'::department_type;
              dept_description := 'Urgent care services';
            ELSE
              dept_type := 'primary_care'::department_type;
              dept_description := 'General healthcare services';
          END CASE;

        WHEN 'specialty' THEN
          -- Determine specialty based on organization name
          IF facility_record.org_name LIKE '%Cardiology%' THEN
            dept_name := 'Cardiology';
            dept_type := 'cardiology'::department_type;
            dept_description := 'Specialized cardiovascular care';
          ELSIF facility_record.org_name LIKE '%Neurology%' THEN
            dept_name := 'Neurology';
            dept_type := 'neurology'::department_type;
            dept_description := 'Specialized neurological care';
          ELSIF facility_record.org_name LIKE '%Orthopedic%' THEN
            dept_name := 'Orthopedics';
            dept_type := 'orthopedics'::department_type;
            dept_description := 'Specialized orthopedic care';
          ELSIF facility_record.org_name LIKE '%Pediatric%' THEN
            dept_name := 'Pediatrics';
            dept_type := 'pediatrics'::department_type;
            dept_description := 'Specialized pediatric care';
          ELSIF facility_record.org_name LIKE '%OB/GYN%' THEN
            dept_name := 'Obstetrics & Gynecology';
            dept_type := 'obstetrics'::department_type;
            dept_description := 'Women\'s health services';
          ELSIF facility_record.org_name LIKE '%Dermatology%' THEN
            dept_name := 'Dermatology';
            dept_type := 'other'::department_type;
            dept_description := 'Skin and dermatological care';
          ELSE
            dept_name := 'Specialty Care';
            dept_type := 'other'::department_type;
            dept_description := 'Specialized medical services';
          END IF;

        ELSE
          dept_name := 'General Medicine';
          dept_type := 'primary_care'::department_type;
          dept_description := 'General medical services';
      END CASE;

      -- Avoid duplicate department names in the same facility
      dept_name := dept_name || CASE WHEN i > 1 THEN ' ' || i::text ELSE '' END;

      -- Insert the department
      INSERT INTO public.departments (
        id,
        facility_id,
        name,
        type,
        settings,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        facility_record.id,
        dept_name,
        dept_type,
        jsonb_build_object(
          'description', dept_description,
          'capacity', 20 + floor(random() * 80)::int,
          'hours', jsonb_build_object(
            'monday', '8:00-17:00',
            'tuesday', '8:00-17:00',
            'wednesday', '8:00-17:00',
            'thursday', '8:00-17:00',
            'friday', '8:00-17:00'
          )
        ),
        NOW() - (random() * interval '365 days'),
        NOW() - (random() * interval '30 days')
      );
    END LOOP;
  END LOOP;
END
$$;

-- Commit transaction
COMMIT;