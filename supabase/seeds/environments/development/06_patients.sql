-- Start transaction
BEGIN;

-- Generate comprehensive patient data for all organizations
DO $$
DECLARE
  org_record RECORD;
  patient_count INTEGER;
  target_count INTEGER;
  i INTEGER;
  first_names_male TEXT[] := ARRAY['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
  first_names_female TEXT[] := ARRAY['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
  last_names <PERSON><PERSON><PERSON>[] := <PERSON><PERSON>['<PERSON>', '<PERSON>', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin', 'Lee', 'Perez', 'Thompson', 'White', 'Harris', 'Sanchez', 'Clark', 'Ramirez', 'Lewis', 'Robinson', 'Walker', 'Young', 'Allen', 'King', 'Wright', 'Scott', 'Torres', 'Nguyen', 'Hill', 'Flores', 'Green', 'Adams', 'Nelson', 'Baker', 'Hall', 'Rivera', 'Campbell', 'Mitchell', 'Carter', 'Roberts'];
  insurance_providers TEXT[] := ARRAY['Blue Cross Blue Shield', 'Aetna', 'United Healthcare', 'Cigna', 'Humana', 'Kaiser Permanente', 'Anthem', 'Molina Healthcare'];
  conditions TEXT[] := ARRAY['Hypertension', 'Type 2 Diabetes', 'Asthma', 'COPD', 'Coronary Artery Disease', 'Arthritis', 'Depression', 'Anxiety', 'Migraine', 'Allergic Rhinitis', 'Gastroesophageal Reflux', 'Osteoporosis', 'Chronic Kidney Disease', 'Atrial Fibrillation', 'Heart Failure'];

  gender_val gender;
  first_name_val TEXT;
  last_name_val TEXT;
  birth_date DATE;
  phone_val TEXT;
  email_val TEXT;
  address_val TEXT;
  emergency_contact_val TEXT;
  insurance_val JSONB;
  medical_history_val JSONB;
BEGIN
  -- Loop through each organization
  FOR org_record IN
    SELECT id, name, type FROM organizations
  LOOP
    -- Count current patients
    SELECT COUNT(*) INTO patient_count
    FROM patients
    WHERE organization_id = org_record.id;

    -- Set target based on organization type
    CASE org_record.type
      WHEN 'hospital' THEN target_count := 75;
      WHEN 'clinic' THEN target_count := 50;
      WHEN 'practice' THEN target_count := 30;
      ELSE target_count := 40;
    END CASE;

    -- Add patients if needed
    IF patient_count < target_count THEN
      FOR i IN 1..(target_count - patient_count) LOOP
        -- Generate random patient data
        gender_val := CASE WHEN random() < 0.5 THEN 'female'::gender ELSE 'male'::gender END;

        IF gender_val = 'female' THEN
          first_name_val := first_names_female[1 + (random() * (array_length(first_names_female, 1) - 1))::int];
        ELSE
          first_name_val := first_names_male[1 + (random() * (array_length(first_names_male, 1) - 1))::int];
        END IF;

        last_name_val := last_names[1 + (random() * (array_length(last_names, 1) - 1))::int];

        -- Generate birth date (ages 1-85)
        birth_date := CURRENT_DATE - (random() * 85 * 365)::int;

        -- Generate contact info
        phone_val := '555-' || LPAD((100 + random() * 899)::int::text, 3, '0') || '-' || LPAD((1000 + random() * 8999)::int::text, 4, '0');
        email_val := lower(first_name_val) || '.' || lower(last_name_val) || '@example.com';
        address_val := (100 + random() * 9899)::int || ' ' ||
                      CASE (random() * 10)::int
                        WHEN 0 THEN 'Main St'
                        WHEN 1 THEN 'Oak Ave'
                        WHEN 2 THEN 'Pine Rd'
                        WHEN 3 THEN 'Elm St'
                        WHEN 4 THEN 'Maple Dr'
                        WHEN 5 THEN 'Cedar Ln'
                        WHEN 6 THEN 'Park Ave'
                        WHEN 7 THEN 'First St'
                        WHEN 8 THEN 'Second Ave'
                        ELSE 'Third St'
                      END || ', ' ||
                      CASE (random() * 5)::int
                        WHEN 0 THEN 'Springfield'
                        WHEN 1 THEN 'Franklin'
                        WHEN 2 THEN 'Georgetown'
                        WHEN 3 THEN 'Madison'
                        ELSE 'Clinton'
                      END || ', ' ||
                      CASE (random() * 10)::int
                        WHEN 0 THEN 'CA'
                        WHEN 1 THEN 'TX'
                        WHEN 2 THEN 'FL'
                        WHEN 3 THEN 'NY'
                        WHEN 4 THEN 'PA'
                        WHEN 5 THEN 'IL'
                        WHEN 6 THEN 'OH'
                        WHEN 7 THEN 'GA'
                        WHEN 8 THEN 'NC'
                        ELSE 'MI'
                      END || ' ' || LPAD((10000 + random() * 89999)::int::text, 5, '0');

        emergency_contact_val :=
          CASE (random() * 4)::int
            WHEN 0 THEN 'Spouse'
            WHEN 1 THEN 'Parent'
            WHEN 2 THEN 'Sibling'
            ELSE 'Child'
          END || ': ' || '555-' || LPAD((100 + random() * 899)::int::text, 3, '0') || '-' || LPAD((1000 + random() * 8999)::int::text, 4, '0');

        -- Generate insurance info
        insurance_val := jsonb_build_object(
          'provider', insurance_providers[1 + (random() * (array_length(insurance_providers, 1) - 1))::int],
          'policy_number', upper(substring(md5(random()::text) from 1 for 10)),
          'group_number', 'GRP' || LPAD((100000 + random() * 899999)::int::text, 6, '0')
        );

        -- Generate medical history (0-3 conditions)
        medical_history_val := jsonb_build_object(
          'conditions',
          CASE (random() * 4)::int
            WHEN 0 THEN '[]'::jsonb
            WHEN 1 THEN jsonb_build_array(conditions[1 + (random() * (array_length(conditions, 1) - 1))::int])
            WHEN 2 THEN jsonb_build_array(
              conditions[1 + (random() * (array_length(conditions, 1) - 1))::int],
              conditions[1 + (random() * (array_length(conditions, 1) - 1))::int]
            )
            ELSE jsonb_build_array(
              conditions[1 + (random() * (array_length(conditions, 1) - 1))::int],
              conditions[1 + (random() * (array_length(conditions, 1) - 1))::int],
              conditions[1 + (random() * (array_length(conditions, 1) - 1))::int]
            )
          END,
          'allergies',
          CASE WHEN random() < 0.3 THEN
            jsonb_build_array(
              CASE (random() * 5)::int
                WHEN 0 THEN 'Penicillin'
                WHEN 1 THEN 'Peanuts'
                WHEN 2 THEN 'Shellfish'
                WHEN 3 THEN 'Latex'
                ELSE 'Sulfa'
              END
            )
          ELSE '[]'::jsonb
          END
        );

        -- Insert the patient
        INSERT INTO public.patients (
          id,
          organization_id,
          first_name,
          last_name,
          date_of_birth,
          gender,
          phone,
          email,
          address,
          emergency_contact,
          insurance_info,
          medical_history,
          created_at,
          updated_at
        ) VALUES (
          uuid_generate_v4(),
          org_record.id,
          first_name_val,
          last_name_val,
          birth_date,
          gender_val,
          phone_val,
          email_val,
          address_val,
          emergency_contact_val,
          insurance_val,
          medical_history_val,
          NOW() - (random() * interval '365 days'),
          NOW() - (random() * interval '30 days')
        );
      END LOOP;
    END IF;
  END LOOP;
END
$$;

COMMIT;